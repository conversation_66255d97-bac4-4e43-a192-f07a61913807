/* STM32F103 MaixCAM通信接收代码
 * 功能：使用DMA环形缓冲区接收MaixCAM发送的检测数据
 * 作者：Alex (Engineer) - 米醋电子工作室
 * 日期：2025-01-31
 */

#include "main.h"
#include <string.h>
#include <stdio.h>

/* 通信协议定义 */
#define FRAME_HEADER_1      0xAA
#define FRAME_HEADER_2      0x55
#define FRAME_TAIL_1        0x55
#define FRAME_TAIL_2        0xAA

#define MAX_SHAPES          10      // 最大形状数量
#define SHAPE_DATA_SIZE     12      // 每个形状数据大小（字节）
#define MAX_PACKET_SIZE     256     // 最大数据包大小

/* 形状类型定义 */
typedef enum {
    SHAPE_NONE = 0,
    SHAPE_TRIANGLE = 1,
    SHAPE_SQUARE = 2,
    SHAPE_CIRCLE = 3
} shape_type_t;

/* 形状数据结构 */
typedef struct {
    shape_type_t type;      // 形状类型
    float height_mm;        // 高度（毫米）
    float width_mm;         // 宽度（毫米）
} shape_data_t;

/* 检测结果数据结构 */
typedef struct {
    float distance_mm;              // 距离（毫米）
    uint8_t shape_count;            // 形状数量
    shape_data_t shapes[MAX_SHAPES]; // 形状数据数组
    uint32_t timestamp;             // 时间戳
} detection_result_t;

/* DMA环形缓冲区 */
#define RX_BUFFER_SIZE      512     // 接收缓冲区大小
static uint8_t rx_buffer[RX_BUFFER_SIZE];
static volatile uint16_t rx_head = 0;      // 缓冲区头指针
static volatile uint16_t rx_tail = 0;      // 缓冲区尾指针

/* 数据包解析状态 */
typedef enum {
    PARSE_HEADER1 = 0,
    PARSE_HEADER2,
    PARSE_LENGTH_LOW,
    PARSE_LENGTH_HIGH,
    PARSE_DATA,
    PARSE_CHECKSUM,
    PARSE_TAIL1,
    PARSE_TAIL2
} parse_state_t;

static parse_state_t parse_state = PARSE_HEADER1;
static uint8_t packet_buffer[MAX_PACKET_SIZE];
static uint16_t packet_length = 0;
static uint16_t data_received = 0;
static uint8_t expected_checksum = 0;

/* 最新检测结果 */
static detection_result_t latest_result;
static volatile uint8_t new_data_flag = 0;

/* 外部变量声明 */
extern UART_HandleTypeDef huart1;  // 与MaixCAM通信的UART
extern DMA_HandleTypeDef hdma_usart1_rx;

/* 函数声明 */
void Communication_Init(void);
void Process_Received_Data(void);
uint8_t Calculate_Checksum(uint8_t* data, uint16_t length);
void Parse_Detection_Packet(uint8_t* data, uint16_t length);
void Print_Detection_Result(detection_result_t* result);
const char* Get_Shape_Name(shape_type_t type);

/**
 * @brief 初始化通信模块
 */
void Communication_Init(void)
{
    // 清空缓冲区
    memset(rx_buffer, 0, RX_BUFFER_SIZE);
    rx_head = 0;
    rx_tail = 0;
    
    // 重置解析状态
    parse_state = PARSE_HEADER1;
    packet_length = 0;
    data_received = 0;
    new_data_flag = 0;
    
    // 启动DMA接收
    HAL_UART_Receive_DMA(&huart1, rx_buffer, RX_BUFFER_SIZE);
    
    printf("MaixCAM通信模块初始化完成\r\n");
    printf("DMA环形缓冲区大小: %d字节\r\n", RX_BUFFER_SIZE);
    printf("等待MaixCAM数据...\r\n");
}

/**
 * @brief 获取环形缓冲区中可用数据长度
 */
uint16_t Get_Available_Data_Length(void)
{
    uint16_t dma_pos = RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(&hdma_usart1_rx);
    
    if (dma_pos >= rx_tail) {
        return dma_pos - rx_tail;
    } else {
        return RX_BUFFER_SIZE - rx_tail + dma_pos;
    }
}

/**
 * @brief 从环形缓冲区读取一个字节
 */
uint8_t Read_Byte_From_Buffer(void)
{
    uint8_t data = rx_buffer[rx_tail];
    rx_tail = (rx_tail + 1) % RX_BUFFER_SIZE;
    return data;
}

/**
 * @brief 计算校验和
 */
uint8_t Calculate_Checksum(uint8_t* data, uint16_t length)
{
    uint32_t sum = 0;
    for (uint16_t i = 0; i < length; i++) {
        sum += data[i];
    }
    return (uint8_t)(sum & 0xFF);
}

/**
 * @brief 解析检测数据包
 */
void Parse_Detection_Packet(uint8_t* data, uint16_t length)
{
    if (length < 5) {  // 最小数据长度：距离(4) + 形状数量(1)
        printf("数据包长度错误: %d\r\n", length);
        return;
    }
    
    uint16_t offset = 0;
    
    // 解析距离（4字节float，小端）
    memcpy(&latest_result.distance_mm, &data[offset], sizeof(float));
    offset += sizeof(float);
    
    // 解析形状数量（1字节）
    latest_result.shape_count = data[offset];
    offset += 1;
    
    // 限制形状数量
    if (latest_result.shape_count > MAX_SHAPES) {
        latest_result.shape_count = MAX_SHAPES;
    }
    
    // 解析形状数据
    for (uint8_t i = 0; i < latest_result.shape_count && offset + SHAPE_DATA_SIZE <= length; i++) {
        // 形状类型（1字节）
        latest_result.shapes[i].type = (shape_type_t)data[offset];
        offset += 1;
        
        // 高度（4字节float，小端）
        memcpy(&latest_result.shapes[i].height_mm, &data[offset], sizeof(float));
        offset += sizeof(float);
        
        // 宽度（4字节float，小端）
        memcpy(&latest_result.shapes[i].width_mm, &data[offset], sizeof(float));
        offset += sizeof(float);
        
        // 跳过保留字节（3字节）
        offset += 3;
    }
    
    // 设置时间戳
    latest_result.timestamp = HAL_GetTick();
    
    // 设置新数据标志
    new_data_flag = 1;
    
    printf("收到检测数据: 距离=%.1fmm, 形状数量=%d\r\n", 
           latest_result.distance_mm, latest_result.shape_count);
}

/**
 * @brief 处理接收到的数据
 */
void Process_Received_Data(void)
{
    uint16_t available = Get_Available_Data_Length();
    
    while (available > 0) {
        uint8_t byte = Read_Byte_From_Buffer();
        available--;
        
        switch (parse_state) {
            case PARSE_HEADER1:
                if (byte == FRAME_HEADER_1) {
                    parse_state = PARSE_HEADER2;
                }
                break;
                
            case PARSE_HEADER2:
                if (byte == FRAME_HEADER_2) {
                    parse_state = PARSE_LENGTH_LOW;
                } else {
                    parse_state = PARSE_HEADER1;
                }
                break;
                
            case PARSE_LENGTH_LOW:
                packet_length = byte;
                parse_state = PARSE_LENGTH_HIGH;
                break;
                
            case PARSE_LENGTH_HIGH:
                packet_length |= (byte << 8);
                if (packet_length > 0 && packet_length < MAX_PACKET_SIZE) {
                    data_received = 0;
                    parse_state = PARSE_DATA;
                } else {
                    parse_state = PARSE_HEADER1;
                }
                break;
                
            case PARSE_DATA:
                packet_buffer[data_received++] = byte;
                if (data_received >= packet_length) {
                    expected_checksum = Calculate_Checksum(packet_buffer, packet_length);
                    parse_state = PARSE_CHECKSUM;
                }
                break;
                
            case PARSE_CHECKSUM:
                if (byte == expected_checksum) {
                    parse_state = PARSE_TAIL1;
                } else {
                    printf("校验和错误: 期望=0x%02X, 实际=0x%02X\r\n", expected_checksum, byte);
                    parse_state = PARSE_HEADER1;
                }
                break;
                
            case PARSE_TAIL1:
                if (byte == FRAME_TAIL_1) {
                    parse_state = PARSE_TAIL2;
                } else {
                    parse_state = PARSE_HEADER1;
                }
                break;
                
            case PARSE_TAIL2:
                if (byte == FRAME_TAIL_2) {
                    // 数据包接收完成，解析数据
                    Parse_Detection_Packet(packet_buffer, packet_length);
                }
                parse_state = PARSE_HEADER1;
                break;
        }
    }
}

/**
 * @brief 获取形状名称字符串
 */
const char* Get_Shape_Name(shape_type_t type)
{
    switch (type) {
        case SHAPE_TRIANGLE: return "Triangle";
        case SHAPE_SQUARE:   return "Square";
        case SHAPE_CIRCLE:   return "Circle";
        default:             return "Unknown";
    }
}

/**
 * @brief 打印检测结果
 */
void Print_Detection_Result(detection_result_t* result)
{
    printf("\r\n=== 检测结果 ===\r\n");
    printf("距离: %.1f mm\r\n", result->distance_mm);
    printf("形状数量: %d\r\n", result->shape_count);
    printf("时间戳: %lu ms\r\n", result->timestamp);
    
    for (uint8_t i = 0; i < result->shape_count; i++) {
        printf("形状%d: %s, 高度=%.1fmm, 宽度=%.1fmm\r\n",
               i + 1,
               Get_Shape_Name(result->shapes[i].type),
               result->shapes[i].height_mm,
               result->shapes[i].width_mm);
    }
    printf("================\r\n\r\n");
}

/**
 * @brief 检查是否有新数据
 */
uint8_t Has_New_Detection_Data(void)
{
    return new_data_flag;
}

/**
 * @brief 获取最新检测结果
 */
detection_result_t* Get_Latest_Detection_Result(void)
{
    new_data_flag = 0;  // 清除新数据标志
    return &latest_result;
}

/**
 * @brief 主循环中调用的通信处理函数
 */
void Communication_Task(void)
{
    // 处理接收到的数据
    Process_Received_Data();
    
    // 检查是否有新的检测结果
    if (Has_New_Detection_Data()) {
        detection_result_t* result = Get_Latest_Detection_Result();
        Print_Detection_Result(result);
        
        // 在这里可以添加您的业务逻辑
        // 例如：根据检测结果控制电机、LED等
    }
}
