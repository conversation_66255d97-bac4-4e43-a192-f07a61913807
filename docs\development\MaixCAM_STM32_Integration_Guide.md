# MaixCAM与STM32通信集成指南

**项目：** MaixCAM形状检测数据传输系统  
**作者：** Alex (Engineer) - 米醋电子工作室  
**日期：** 2025-01-31  
**版本：** v1.0  

## 项目概述

本项目实现了MaixCAM与STM32F103之间的高效通信，将MaixCAM检测到的形状数据（距离、高度、宽度）实时传输给STM32进行处理。

### 主要功能
- MaixCAM实时形状检测（三角形、正方形、圆形）
- 基于A4纸标定的距离和尺寸测量
- 可靠的二进制通信协议
- STM32 DMA环形缓冲区接收
- 数据包完整性校验
- 实时数据处理和显示

## 硬件连接

### 引脚连接
```
MaixCAM          STM32F103
--------         ---------
UART TX    -->   PA10 (USART1_RX)
UART RX    <--   PA9  (USART1_TX)
GND        ---   GND
```

### 通信参数
- **波特率：** 115200
- **数据位：** 8
- **停止位：** 1
- **校验位：** 无
- **流控制：** 无

## 通信协议

### 数据包格式
```
+--------+--------+--------+--------+--------+--------+--------+--------+
| 帧头1  | 帧头2  | 长度低 | 长度高 |      数据内容      | 校验和 | 帧尾1  | 帧尾2  |
| 0xAA   | 0x55   | L_Low  | L_High |    Data Content    |  CRC   | 0x55   | 0xAA   |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### 数据内容格式
```
+--------+--------+--------+--------+--------+
| 距离(4B float) | 形状数量(1B) | 形状数据(变长) |
+--------+--------+--------+--------+--------+
```

### 形状数据格式（每个形状12字节）
```
+--------+--------+--------+--------+
| 类型(1B) | 高度(4B float) | 宽度(4B float) | 保留(3B) |
+--------+--------+--------+--------+
```

### 形状类型定义
- `0x00`: 未知形状
- `0x01`: 三角形
- `0x02`: 正方形  
- `0x03`: 圆形

## MaixCAM代码修改

### 主要修改点

1. **添加UART初始化**
```python
# 初始化UART通信
self.uart_device = "/dev/ttyS0"  # 使用UART0
self.serial = uart.UART(self.uart_device, 115200)
```

2. **数据发送函数**
```python
def send_detection_data(self, distance, shapes_data):
    # 构建二进制数据包
    # 发送到STM32
    self.serial.write(packet)
```

3. **集成到主循环**
```python
# 在process_frame函数中调用
if R_distance is not None and len(detected_shapes) > 0:
    self.send_detection_data(R_distance, detected_shapes)
```

### 使用方法
1. 将修改后的代码部署到MaixCAM
2. 确保硬件连接正确
3. 运行程序开始检测和传输

## STM32代码集成

### 主要功能模块

1. **DMA环形缓冲区**
   - 512字节缓冲区
   - 自动循环接收
   - 防止数据丢失

2. **数据包解析状态机**
   - 8个解析状态
   - 完整性校验
   - 错误恢复

3. **数据结构定义**
```c
typedef struct {
    float distance_mm;              // 距离（毫米）
    uint8_t shape_count;            // 形状数量
    shape_data_t shapes[MAX_SHAPES]; // 形状数据数组
    uint32_t timestamp;             // 时间戳
} detection_result_t;
```

### 集成步骤

1. **包含头文件和定义**
   - 在main.c中添加通信协议定义
   - 添加全局变量和函数声明

2. **初始化通信模块**
```c
// 在main函数中调用
Communication_Init();
```

3. **主循环处理**
```c
while (1) {
    Communication_Task();  // 处理通信
    HAL_Delay(1);         // 小延时
}
```

4. **printf重定向**
```c
int _write(int file, char *ptr, int len) {
    HAL_UART_Transmit(&huart2, (uint8_t*)ptr, len, HAL_MAX_DELAY);
    return len;
}
```

## 关键函数说明

### MaixCAM端

- `send_detection_data()`: 发送检测数据到STM32
- `process_frame()`: 主要的图像处理和数据发送逻辑

### STM32端

- `Communication_Init()`: 初始化通信模块
- `Communication_Task()`: 主循环通信处理
- `Process_Received_Data()`: 数据包解析
- `Parse_Detection_Packet()`: 检测数据解析
- `Print_Detection_Result()`: 打印检测结果

## 数据流程

1. **MaixCAM检测**
   - 摄像头捕获图像
   - 检测A4纸外框
   - 计算距离
   - 检测内部形状
   - 计算形状尺寸

2. **数据打包**
   - 构建二进制数据包
   - 添加帧头、长度、校验和、帧尾
   - 通过UART发送

3. **STM32接收**
   - DMA自动接收到环形缓冲区
   - 状态机解析数据包
   - 校验数据完整性
   - 提取检测结果

4. **数据处理**
   - 打印检测结果
   - 根据形状类型执行相应逻辑
   - 可扩展业务处理

## 性能特点

### MaixCAM端
- **实时性：** 支持实时图像处理和数据传输
- **准确性：** 基于A4纸标定的精确测量
- **稳定性：** 完整的错误处理机制

### STM32端
- **高效性：** DMA自动接收，CPU占用低
- **可靠性：** 环形缓冲区防止数据丢失
- **扩展性：** 模块化设计，易于扩展功能

## 调试和测试

### 调试输出
STM32通过UART2输出调试信息：
```
STM32F103 MaixCAM通信系统启动
等待MaixCAM检测数据...
收到检测数据: 距离=245.6mm, 形状数量=2
=== 检测结果 ===
距离: 245.6 mm
形状数量: 2
时间戳: 12345 ms
形状1: Triangle, 高度=25.4mm, 宽度=23.1mm
形状2: Square, 高度=30.2mm, 宽度=29.8mm
================
```

### 测试建议
1. 使用标准A4纸进行距离标定测试
2. 测试不同形状的检测准确性
3. 验证通信协议的可靠性
4. 测试长时间运行的稳定性

## 故障排除

### 常见问题

1. **无法接收数据**
   - 检查硬件连接
   - 确认波特率设置
   - 检查UART初始化

2. **数据包解析错误**
   - 检查校验和计算
   - 确认数据包格式
   - 检查字节序（小端）

3. **检测精度问题**
   - 重新校准A4纸参数
   - 调整检测阈值
   - 优化光照条件

## 扩展功能

### 可能的扩展方向
1. 添加更多形状类型检测
2. 支持多目标跟踪
3. 增加颜色识别功能
4. 实现双向通信
5. 添加数据存储功能

## 版权信息

**版权所有：** 米醋电子工作室  
**开发团队：** Mike (Team Leader), Alex (Engineer)  
**技术支持：** 基于MaixPy和STM32 HAL库  

---

*本文档提供了完整的MaixCAM与STM32通信集成方案，包含详细的代码实现和使用说明。*
