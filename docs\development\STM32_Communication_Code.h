/* STM32F103 MaixCAM通信接收代码头文件
 * 功能：MaixCAM通信协议定义和函数声明
 * 作者：Alex (Engineer) - 米醋电子工作室
 * 日期：2025-01-31
 */

#ifndef __STM32_COMMUNICATION_CODE_H
#define __STM32_COMMUNICATION_CODE_H

#include "main.h"

/* 通信协议常量定义 */
#define FRAME_HEADER_1      0xAA
#define FRAME_HEADER_2      0x55
#define FRAME_TAIL_1        0x55
#define FRAME_TAIL_2        0xAA

#define MAX_SHAPES          10      // 最大形状数量
#define SHAPE_DATA_SIZE     12      // 每个形状数据大小（字节）
#define MAX_PACKET_SIZE     256     // 最大数据包大小
#define RX_BUFFER_SIZE      512     // 接收缓冲区大小

/* 形状类型枚举 */
typedef enum {
    SHAPE_NONE = 0,
    SHAPE_TRIANGLE = 1,
    SHAPE_SQUARE = 2,
    SHAPE_CIRCLE = 3
} shape_type_t;

/* 形状数据结构 */
typedef struct {
    shape_type_t type;      // 形状类型
    float height_mm;        // 高度（毫米）
    float width_mm;         // 宽度（毫米）
} shape_data_t;

/* 检测结果数据结构 */
typedef struct {
    float distance_mm;              // 距离（毫米）
    uint8_t shape_count;            // 形状数量
    shape_data_t shapes[MAX_SHAPES]; // 形状数据数组
    uint32_t timestamp;             // 时间戳
} detection_result_t;

/* 数据包解析状态枚举 */
typedef enum {
    PARSE_HEADER1 = 0,
    PARSE_HEADER2,
    PARSE_LENGTH_LOW,
    PARSE_LENGTH_HIGH,
    PARSE_DATA,
    PARSE_CHECKSUM,
    PARSE_TAIL1,
    PARSE_TAIL2
} parse_state_t;

/* 函数声明 */

/**
 * @brief 初始化通信模块
 */
void Communication_Init(void);

/**
 * @brief 主循环中调用的通信处理函数
 */
void Communication_Task(void);

/**
 * @brief 处理接收到的数据
 */
void Process_Received_Data(void);

/**
 * @brief 检查是否有新数据
 * @return 1: 有新数据, 0: 无新数据
 */
uint8_t Has_New_Detection_Data(void);

/**
 * @brief 获取最新检测结果
 * @return 指向最新检测结果的指针
 */
detection_result_t* Get_Latest_Detection_Result(void);

/**
 * @brief 获取环形缓冲区中可用数据长度
 * @return 可用数据长度
 */
uint16_t Get_Available_Data_Length(void);

/**
 * @brief 从环形缓冲区读取一个字节
 * @return 读取的字节
 */
uint8_t Read_Byte_From_Buffer(void);

/**
 * @brief 计算校验和
 * @param data 数据指针
 * @param length 数据长度
 * @return 校验和
 */
uint8_t Calculate_Checksum(uint8_t* data, uint16_t length);

/**
 * @brief 解析检测数据包
 * @param data 数据指针
 * @param length 数据长度
 */
void Parse_Detection_Packet(uint8_t* data, uint16_t length);

/**
 * @brief 打印检测结果
 * @param result 检测结果指针
 */
void Print_Detection_Result(detection_result_t* result);

/**
 * @brief 获取形状名称字符串
 * @param type 形状类型
 * @return 形状名称字符串
 */
const char* Get_Shape_Name(shape_type_t type);

#endif /* __STM32_COMMUNICATION_CODE_H */
