# STM32 OLED显示模块集成指南

**功能：** 在STM32F103上集成OLED显示模块，显示HELLO WORLD  
**作者：** Alex (Engineer) - 米醋电子工作室  
**日期：** 2025-01-31  
**版本：** v1.0  

## 功能概述

成功将OLED显示模块集成到STM32F103项目中，支持文本显示功能，并在启动时显示欢迎信息。

## 硬件连接

### OLED模块连接 (I2C接口)
```
OLED模块        STM32F103
---------      -----------
VCC      →     3.3V
GND      →     GND
SCL      →     PB12 (OLED_SCL_Pin)
SDA      →     PB13 (OLED_SDA_Pin)
```

### 引脚配置
- **SCL (时钟线)**：PB12 - GPIO输出模式
- **SDA (数据线)**：PB13 - GPIO输出模式
- **I2C地址**：0x78 (7位地址0x3C左移1位)

## 文件结构

### 新增文件
```
../Core/Inc/OLED.h          - OLED函数声明
../Core/Inc/OLED_Font.h     - OLED字体库
../Core/Src/OLED.c          - OLED驱动实现
```

### 项目配置
- **F103 DATA.uvprojx**：已添加OLED.c到编译列表
- **main.c**：已包含OLED.h头文件

## 显示内容

### 启动显示
```
第1行：HELLO WORLD
第2行：STM32F103
第3行：UART Forward
第4行：Ready!
```

### 显示区域
- **分辨率**：128x64像素
- **字符大小**：8x16像素
- **显示行数**：4行
- **每行字符数**：16个字符

## 核心函数

### 初始化函数
```c
void OLED_Init(void)
{
    // Power-on delay
    // I2C pin initialization
    // OLED controller initialization
    // Clear screen
}
```

### 显示函数
```c
void OLED_ShowString(uint8_t Line, uint8_t Column, char *String)
void OLED_ShowChar(uint8_t Line, uint8_t Column, char Char)
void OLED_ShowNum(uint8_t Line, uint8_t Column, uint32_t Number, uint8_t Length)
void OLED_Clear(void)
```

### I2C通信函数
```c
void OLED_I2C_Start(void)      // I2C开始条件
void OLED_I2C_Stop(void)       // I2C停止条件
void OLED_I2C_SendByte(uint8_t Byte)  // 发送字节
```

## 初始化序列

### main.c中的初始化顺序
```c
/* USER CODE BEGIN 2 */
// 1. Initialize OLED display
OLED_Init();
OLED_Clear();
OLED_ShowString(1, 1, "HELLO WORLD");
OLED_ShowString(2, 1, "STM32F103");
OLED_ShowString(3, 1, "UART Forward");
OLED_ShowString(4, 1, "Ready!");

// 2. Initialize UART forwarding
UART_Forward_Init();
printf("STM32F103 UART Forward Started\r\n");
printf("USART1 -> USART2 forwarding enabled\r\n");
printf("OLED Display initialized\r\n");
/* USER CODE END 2 */
```

## 技术特点

### OLED控制器
- **型号**：SSD1306兼容
- **接口**：I2C (软件模拟)
- **分辨率**：128x64
- **颜色**：单色 (通常为蓝色或白色)

### I2C通信
- **模式**：软件模拟I2C
- **速度**：标准模式
- **地址**：0x78 (写地址)
- **数据格式**：命令/数据分离

### 字体库
- **字符集**：ASCII可见字符 (32-126)
- **字体大小**：8x16像素
- **存储方式**：点阵数组

## 显示坐标系统

### 坐标说明
```
Line (行)：1-4 (每行16像素高)
Column (列)：1-16 (每列8像素宽)

(1,1)  (1,2)  ... (1,16)
(2,1)  (2,2)  ... (2,16)
(3,1)  (3,2)  ... (3,16)
(4,1)  (4,2)  ... (4,16)
```

### 像素坐标
```
X: 0-127 (水平方向)
Y: 0-63  (垂直方向，按页分为8页，每页8像素)
```

## 使用示例

### 显示文本
```c
OLED_ShowString(1, 1, "Hello");      // 第1行第1列显示"Hello"
OLED_ShowString(2, 5, "World");      // 第2行第5列显示"World"
```

### 显示数字
```c
OLED_ShowNum(3, 1, 12345, 5);        // 显示5位数字
OLED_ShowSignedNum(4, 1, -123, 3);   // 显示带符号数字
OLED_ShowHexNum(1, 10, 0xFF, 2);     // 显示16进制数字
```

### 清屏
```c
OLED_Clear();                        // 清除整个屏幕
```

## 故障排除

### 常见问题

1. **OLED无显示**
   - 检查电源连接 (3.3V, GND)
   - 确认I2C线连接正确 (SCL->PB12, SDA->PB13)
   - 检查OLED模块是否损坏

2. **显示乱码**
   - 确认字体库正确包含
   - 检查字符范围 (ASCII 32-126)
   - 验证I2C通信时序

3. **编译错误**
   - 确认OLED.c已添加到项目
   - 检查头文件路径
   - 验证函数声明与定义匹配

### 调试方法
1. **逐步测试**：先测试OLED_Init()，再测试显示函数
2. **示波器检查**：监控SCL和SDA信号
3. **逻辑分析仪**：分析I2C通信协议

## 性能优化

### 显示优化
- **批量更新**：一次性更新多个字符
- **局部刷新**：只更新变化的区域
- **缓存机制**：使用显示缓存减少I2C通信

### 代码优化
- **内联函数**：频繁调用的函数使用内联
- **查表法**：使用查表替代计算
- **DMA支持**：考虑使用硬件I2C和DMA

## 扩展功能

### 可能的增强
1. **图形显示**：添加画点、画线、画矩形功能
2. **中文支持**：添加中文字体库
3. **动画效果**：实现滚动、闪烁等效果
4. **菜单系统**：实现多级菜单显示

### 与UART转发集成
- **状态显示**：在OLED上显示UART转发状态
- **数据计数**：显示转发的数据量
- **错误提示**：显示通信错误信息

## 项目集成状态

### 当前功能
- ✅ OLED基本显示功能
- ✅ 启动欢迎信息
- ✅ UART数据转发功能
- ✅ printf调试输出

### 硬件配置
- ✅ PB12/PB13 GPIO配置
- ✅ USART1/USART2配置
- ✅ DMA配置

### 编译配置
- ✅ OLED.c添加到项目
- ✅ 头文件路径正确
- ✅ 无编译错误

## 测试验证

### 基本测试
1. **编译烧录**程序到STM32
2. **连接OLED**到PB12/PB13
3. **上电观察**：应该看到HELLO WORLD等文本
4. **UART测试**：USART1到USART2转发功能正常

### 预期结果
- OLED显示4行文本
- UART转发功能正常
- 调试信息通过printf输出

## 版权信息

**版权所有：** 米醋电子工作室  
**开发团队：** Mike (Team Leader), Alex (Engineer)  

---

*OLED模块已成功集成到STM32F103项目中，支持文本显示和UART数据转发功能。*
