# MaixCAM与STM32通信协议深度学习文档

## 文档信息
- **版本**: v1.0.0
- **创建日期**: 2025-01-31
- **负责人**: Alex (Engineer)
- **项目**: MaixCAM与STM32通信开发
- **学习来源**: https://wiki.sipeed.com/maixpy/doc/zh/peripheral/uart.html

---

## 1. 通信协议基础概念

### 1.1 通信协议层次结构
为了让两个设备能够实现稳定通信，一般从底往上有几个层次：

1. **硬件层**: 比如 UART 使用 TX、RX、GND 三根线，也有可能是无线的，比如 WiFi
2. **传输层**: 使用传输控制协议来实现数据的稳定传输，比如 UART 协议规定了波特率、停止位、校验位
3. **应用层**: 从传输层获得的数据是流式数据，应用会自己定义一份应用层通信协议来规范传输的内容

### 1.2 UART串口基础
串口是一种通信方式，包含了硬件和通信协议的定义：

#### 硬件组成：
- **3个引脚**: GND、RX、TX
- **连接方式**: 通信双方**交叉连接** RX TX，即一方 TX 发送到另一方的 RX，双方 GND 连接到一起
- **控制器**: 一般在芯片内部，也叫 UART 外设，一个芯片有一个或多个 UART 控制器

#### 通信协议参数：
- **波特率**: 最重要的参数，决定通信速度
- **校验位**: 用于数据校验
- **停止位**: 数据帧结束标志

---

## 2. MaixCAM硬件接口详解

### 2.1 MaixCAM串口引脚分布
- **默认串口**: 从USB口引出一个串口（UART0）
- **引脚对应**: A16(TX) 和 A17(RX)引脚，与USB口引出的是同样的引脚
- **设备路径**: `/dev/ttyS0`

### 2.2 硬件连接注意事项

#### Type-C转接板注意点：
- Type-C 正插和反插，转接小板上的 RX 和 TX 会交换
- 默认 **Type-C 母口朝前** 和丝印符合
- 如果无法通信，可能是 RX TX 反了，尝试将 Type-C 翻转一面插

#### TX引脚重要警告：
⚠️ **MaixCAM 的 TX(UART0) 引脚在开机时不能是被拉低的状态，否则会导致无法开机**
- 这是芯片的特性
- 做 3.3V 转 5V 的电平转换电路时要十分注意不要默认拉低，请保持浮空
- 如果无法开机，检查 TX 是否被拉低

### 2.3 开机日志输出
- **MaixCAM 的串口0 在开机时会打印一部分开机日志**
- 启动完毕后会打印 `serial ready` 字样
- 与单片机通信需要注意丢弃这部分信息
- 可以通过查看串口0的开机打印来诊断系统启动问题

---

## 3. MaixPy中UART使用详解

### 3.1 基础使用方法

```python
from maix import uart

# 初始化串口
device = "/dev/ttyS0"  # 第一个串口
serial = uart.UART(device, 115200)  # 波特率115200

# 发送数据
serial.write_str("hello world")

# 接收数据
data = serial.read(timeout=2000)
print("received:", data)
```

### 3.2 数据发送方法

#### 发送字符串：
```python
# 方法1：使用write_str
serial.write_str("Hello MaixCAM!")

# 方法2：使用write + encode
content = "Hello MaixCAM!"
serial.write(content.encode())
```

#### 发送字节数据：
```python
# 发送字节流
bytes_content = b'\x01\x02\x03'
serial.write(bytes_content)
```

#### 发送格式化数据：
```python
# 字符串格式化
num = 10
content = "I have {} apple".format(num)
content2 = f"I have {num} apple"
content3 = "I have {:04d} apple".format(num)  # 补零到4位
serial.write_str(content)
```

#### 发送二进制编码数据：
```python
from struct import pack

num = 10
bytes_content = b'\xAA\xBB\xCC\xDD'  # 帧头
bytes_content += pack("<i", num)      # 小端编码的int数据
bytes_content += b'\xFF'              # 帧尾
serial.write(bytes_content)
```

### 3.3 数据接收方法

#### 基础接收：
```python
while not app.need_exit():
    data = serial.read()
    if data:
        print(data)
    time.sleep_ms(1)  # 释放CPU资源
```

#### read函数参数详解：
- **len**: 想接收的最大长度，默认-1代表缓冲区有多少就返回多少
- **timeout**: 
  - 默认0：立即返回
  - <0：一直等待直到接收到数据
  - >0：超过指定时间就返回

#### 常用参数组合：
```python
# 立即返回缓冲区数据
data = serial.read()  # 等同于 read(-1, 0)

# 阻塞式读取一串数据
data = serial.read(len=-1, timeout=-1)

# 阻塞式读取指定长度或超时
data = serial.read(len=10, timeout=1000)  # 读取10个字符或1秒超时
```

### 3.4 回调函数接收

```python
from maix import uart, app, time

def on_received(serial: uart.UART, data: bytes):
    print("received:", data)
    # 回传数据
    serial.write(data)

device = "/dev/ttyS0"
serial = uart.UART(device, 115200)
serial.set_received_callback(on_received)

serial.write_str("hello\r\n")
print("sent hello")

while not app.need_exit():
    time.sleep_ms(100)  # 释放CPU资源
```

**注意事项**：
- 回调函数在**另外一个线程**里调用
- 使用回调函数时请不要再使用read函数读取
- 注意多线程常见问题

### 3.5 使用其它串口

```python
from maix import app, uart, pinmap, time

# 设置引脚映射为UART1功能
pinmap.set_pin_function("A18", "UART1_RX")
pinmap.set_pin_function("A19", "UART1_TX")

# 使用UART1
device = "/dev/ttyS1"
serial1 = uart.UART(device, 115200)
```

---

## 4. 应用层通信协议设计

### 4.1 字符协议 vs 二进制协议

#### 字符协议特点：
- **优点**: 简单，人眼能直接看懂
- **缺点**: 占用字符数量不固定，数据量比较大
- **示例**: `$10,20*` 表示坐标(10,20)

```python
# 字符协议编码示例
x = 10
y = 20
content = "${},{}*".format(x, y)
print(content)  # 输出: $10,20*
```

#### 二进制协议特点：
- **优点**: 数据量小，传输效率高
- **缺点**: 需要编解码，相对复杂
- **示例**: `0x24 0x03 0xE8` 表示 $ + 1000(小端编码)

```python
from struct import pack

# 二进制协议编码示例
num = 1000
b = pack("<H", num)  # <H表示小端编码的uint16
print(b)  # 输出: b'\xe8\x03'
```

### 4.2 协议设计要素

#### 基本协议结构：
1. **帧头**: 标识数据包开始，如 `$`
2. **数据内容**: 实际要传输的数据
3. **校验值**: 用于验证数据完整性
4. **帧尾**: 标识数据包结束，如 `*`

#### 校验值计算示例：
```python
# 简单校验值计算
data = b'$10,20'
checksum = sum(data) % 255
final_data = f"$10,20,{checksum}*"
```

---

## 5. Maix应用通信协议

### 5.1 协议概述
Maix应用通信协议是一个应用层通信协议，传输层基于UART或TCP：
- 二进制协议
- 包括帧头、数据内容、校验等
- 完整协议定义：[Maix应用通信协议标准](https://wiki.sipeed.com/maixcdk/doc/zh/convention/protocol.html)

### 5.2 实际应用示例：物体检测结果传输

```python
from maix import comm, protocol, app
from maix.err import Err
import struct

def encode_objs(objs):
    '''
    编码物体信息为字节流
    格式: 2B x(LE) + 2B y(LE) + 2B w(LE) + 2B h(LE) + 2B idx ...
    '''
    body = b""
    for obj in objs:
        body += struct.pack("<hhHHH", obj.x, obj.y, obj.w, obj.h, obj.class_id)
    return body

# 自定义命令定义
APP_CMD_ECHO = 0x01        # 测试命令
APP_CMD_DETECT_RES = 0x02  # 检测结果命令

# 初始化通信协议
p = comm.CommProtocol(buff_size=1024)

# 在检测循环中发送结果
while not app.need_exit():
    # ... 物体检测代码 ...
    objs = detector.detect(img, conf_th=0.5, iou_th=0.45)
    if len(objs) > 0:
        body = encode_objs(objs)
        p.report(APP_CMD_DETECT_RES, body)
```

### 5.3 数据编码格式说明

#### 物体信息编码格式：
- 每个物体占用 10 个字节
- 数据格式：`x(2B) + y(2B) + w(2B) + h(2B) + class_id(2B)`
- 编码方式：小端编码（LE）
- 总物体数量：`body_len / 10`

#### 接收端解码：
```python
# MaixPy接收端
while not app.need_exit():
    msg = p.get_msg()
    if msg and msg.is_report and msg.cmd == APP_CMD_DETECT_RES:
        print("receive objs:", decode_objs(msg.get_body()))
        p.resp_ok(msg.cmd, b'1')
```

---

## 6. STM32端实现要点

### 6.1 硬件连接
```
MaixCAM          STM32
TX(A16)    -->   RX
RX(A17)    <--   TX
GND        ---   GND
```

### 6.2 STM32代码框架
```c
// 基础UART初始化
void UART_Init(void) {
    // 配置UART参数：115200, 8N1
    // 使能UART接收中断
}

// 数据接收处理
void UART_IRQHandler(void) {
    if (UART_GetITStatus(UART1, UART_IT_RXNE) != RESET) {
        uint8_t data = UART_ReceiveData(UART1);
        // 处理接收到的数据
        protocol_parse_byte(data);
    }
}

// 物体信息解码
typedef struct {
    int16_t x, y;
    uint16_t w, h;
    uint16_t class_id;
} object_info_t;

void decode_objects(uint8_t* data, uint16_t len) {
    uint16_t obj_count = len / 10;
    for (uint16_t i = 0; i < obj_count; i++) {
        object_info_t obj;
        memcpy(&obj, data + i * 10, sizeof(object_info_t));
        // 处理物体信息
        printf("Object %d: x=%d, y=%d, w=%d, h=%d, class=%d\n",
               i, obj.x, obj.y, obj.w, obj.h, obj.class_id);
    }
}
```

---

## 7. 开发调试技巧

### 7.1 串口调试工具
- 使用USB转串口小板连接电脑进行调试
- 推荐工具：串口调试助手、PuTTY、minicom

### 7.2 常见问题排查
1. **无法通信**：
   - 检查RX/TX是否交叉连接
   - 检查波特率是否一致
   - 检查GND是否连接

2. **数据丢失**：
   - 检查缓冲区大小
   - 添加流控制
   - 降低发送频率

3. **开机无法启动**：
   - 检查TX引脚是否被拉低
   - 断开外部连接重新上电测试

### 7.3 性能优化建议
- 使用二进制协议减少数据量
- 合理设置缓冲区大小
- 使用DMA减少CPU占用
- 添加数据压缩算法

---

## 8. 项目实战规划

### 8.1 开发阶段划分
1. **基础通信测试**: 实现简单的字符串收发
2. **协议实现**: 实现Maix通信协议的编解码
3. **功能集成**: 集成AI检测结果传输
4. **性能优化**: 优化传输效率和稳定性
5. **异常处理**: 添加错误检测和恢复机制

### 8.2 测试验证计划
- 单元测试：各个函数模块独立测试
- 集成测试：MaixCAM与STM32联合测试
- 压力测试：高频数据传输测试
- 异常测试：断线重连、数据错误恢复测试

---

## 9. 总结与下一步

### 9.1 关键知识点总结
1. **UART硬件基础**: 引脚连接、电平转换、时序参数
2. **MaixPy UART API**: 初始化、发送、接收、回调函数
3. **通信协议设计**: 字符协议vs二进制协议、帧结构设计
4. **Maix应用协议**: 标准化的应用层协议实现
5. **STM32集成**: C语言端的协议实现要点

### 9.2 下一步开发计划
1. 搭建硬件测试环境
2. 实现基础UART通信测试
3. 移植Maix通信协议到STM32
4. 集成AI检测功能
5. 性能测试和优化

---

## 10. 参考资料
- [MaixPy UART官方文档](https://wiki.sipeed.com/maixpy/doc/zh/peripheral/uart.html)
- [Maix应用通信协议标准](https://wiki.sipeed.com/maixcdk/doc/zh/convention/protocol.html)
- [MaixPy通信协议示例](https://github.com/sipeed/MaixPy/tree/main/examples/protocol/comm_protocol_yolov5.py)
- MaixCAM硬件接口图和引脚定义

**文档状态**: ✅ 学习完成，已全面掌握MaixCAM与STM32通信协议基础知识，准备进入实际开发阶段。
