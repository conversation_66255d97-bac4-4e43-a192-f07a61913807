# STM32 USART2测试指南

**功能：** STM32通过USART2发送测试数据"10086"  
**作者：** Alex (Engineer) - 米醋电子工作室  
**日期：** 2025-01-31  
**版本：** v1.0  

## 测试概述

当前STM32代码已简化为最基本的USART2测试功能，每秒通过USART2发送"10086\r\n"字符串。

## 功能说明

### 当前代码行为
1. **初始化**：系统启动后初始化USART2
2. **循环发送**：每1秒通过USART2发送"10086\r\n"
3. **调试输出**：通过printf输出测试状态（如果连接了调试器）

### 发送内容
- **数据**：字符串"10086"
- **结尾**：回车换行符"\r\n"
- **频率**：每秒1次
- **波特率**：115200

## 硬件连接

### USART2引脚
```
STM32F103        外部设备/PC
---------        -----------
PA2 (TX)    →    RX
PA3 (RX)    ←    TX (可选，当前未使用)
GND         ---  GND
```

### 测试设备选项
1. **USB转TTL模块**：连接到PC串口助手
2. **另一个STM32**：作为接收端
3. **Arduino**：作为接收端
4. **逻辑分析仪**：查看波形

## 验证方法

### 使用串口助手
1. 连接USB转TTL到STM32的PA2(TX)和GND
2. 打开串口助手，设置波特率115200
3. 应该看到每秒输出一次"10086"

### 预期输出
```
10086
10086
10086
10086
...
```

## 代码位置

### 主要修改位置

**初始化部分（第189-194行）：**
```c
/* USER CODE BEGIN 2 */
// 简单测试：USART2发送10086
printf("STM32F103 USART2测试启动\r\n");
printf("开始发送10086到USART2...\r\n");
/* USER CODE END 2 */
```

**主循环部分（第202-209行）：**
```c
/* USER CODE BEGIN 3 */
// 简单测试：通过USART2发送10086
char test_data[] = "10086\r\n";
HAL_UART_Transmit(&huart2, (uint8_t*)test_data, strlen(test_data), HAL_MAX_DELAY);

// 延时1秒
HAL_Delay(1000);
/* USER CODE END 3 */
```

## 技术细节

### UART配置
- **波特率**：115200
- **数据位**：8
- **停止位**：1
- **校验位**：无
- **流控制**：无

### 发送函数
```c
HAL_UART_Transmit(&huart2, (uint8_t*)test_data, strlen(test_data), HAL_MAX_DELAY);
```

### 参数说明
- `&huart2`：USART2句柄
- `(uint8_t*)test_data`：要发送的数据
- `strlen(test_data)`：数据长度
- `HAL_MAX_DELAY`：最大等待时间

## 故障排除

### 常见问题

1. **无数据输出**
   - 检查硬件连接（PA2是否正确连接）
   - 确认波特率设置（115200）
   - 检查GND是否共地

2. **数据乱码**
   - 确认波特率匹配
   - 检查数据位、停止位、校验位设置
   - 确认接收端配置正确

3. **发送不规律**
   - 检查系统时钟配置
   - 确认HAL_Delay函数工作正常

### 调试方法
1. **LED指示**：可以添加LED闪烁来确认程序运行
2. **示波器**：测量PA2引脚的波形
3. **逻辑分析仪**：分析UART时序

## 测试步骤

### 基本测试
1. 编译并烧录程序到STM32
2. 连接USART2到测试设备
3. 打开串口助手或终端
4. 观察是否每秒收到"10086"

### 进阶测试
1. 修改发送内容测试
2. 修改发送频率测试
3. 测试不同波特率
4. 测试长时间稳定性

## 后续扩展

当基本测试通过后，可以：
1. 恢复MaixCAM通信功能
2. 添加数据转发功能
3. 实现双向通信
4. 添加更复杂的数据处理

## 代码恢复

如需恢复完整功能，可以：
1. 恢复Communication_Init()调用
2. 恢复Communication_Task()调用
3. 重新启用数据转发功能

## 版权信息

**版权所有：** 米醋电子工作室  
**开发团队：** Mike (Team Leader), Alex (Engineer)  

---

*这是一个简化的USART2测试版本，用于验证基本的串口发送功能。*
