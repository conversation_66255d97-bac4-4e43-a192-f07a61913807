# MaixCAM代码修改说明

**针对用户原始代码的具体修改指导**

## 需要添加的导入

在您的代码开头添加：
```python
from maix import uart
import struct
```

## 需要添加的初始化代码

在`__init__`方法中添加UART初始化：
```python
def __init__(self, width=640, height=480):
    # ... 您的原有初始化代码 ...
    
    # 添加UART通信初始化
    self.uart_device = "/dev/ttyS0"  # 使用UART0
    self.serial = uart.UART(self.uart_device, 115200)
    print(f"UART初始化完成: {self.uart_device}, 波特率: 115200")
```

## 需要添加的数据发送函数

在您的类中添加这个新函数：
```python
def send_detection_data(self, distance, shapes_data):
    """
    发送检测数据到STM32
    参数：
    - distance: R_distance值
    - shapes_data: 包含形状信息的列表，每个元素需要有height_mm和width_mm属性
    """
    try:
        # 帧头
        frame_header = b'\xAA\x55'
        
        # 计算数据长度
        shapes_count = len(shapes_data)
        shape_data_len = shapes_count * 12  # 每个形状12字节
        data_len = 4 + 1 + shape_data_len  # 距离 + 数量 + 形状数据
        
        # 构建数据包
        packet = frame_header
        packet += struct.pack('<H', data_len)  # 数据长度（小端）
        packet += struct.pack('<f', distance)  # 距离（小端float）
        packet += struct.pack('<B', shapes_count)  # 形状数量
        
        # 添加形状数据
        for shape in shapes_data:
            shape_type = 0  # 默认类型
            if hasattr(shape, 'name'):
                if shape['name'] == 'Triangle':
                    shape_type = 1
                elif shape['name'] == 'Square':
                    shape_type = 2
                elif shape['name'] == 'Circle':
                    shape_type = 3
            
            packet += struct.pack('<B', shape_type)  # 形状类型
            packet += struct.pack('<f', shape['height_mm'])  # 高度
            packet += struct.pack('<f', shape['width_mm'])   # 宽度
            packet += b'\x00\x00\x00'  # 保留3字节
        
        # 计算校验和
        checksum = sum(packet[4:]) & 0xFF  # 从数据长度开始计算校验和
        packet += struct.pack('<B', checksum)
        
        # 帧尾
        packet += b'\x55\xAA'
        
        # 发送数据包
        self.serial.write(packet)
        
        # 调试信息
        print(f"发送数据包: 距离={distance:.1f}mm, 形状数量={shapes_count}")
        
    except Exception as e:
        print(f"发送数据包失败: {e}")
```

## 需要修改的主要位置

在您的`process_frame`方法中，找到计算`R_distance`和形状信息的位置，添加数据发送调用：

```python
def process_frame(self, frame):
    # ... 您的原有代码 ...
    
    # 在您计算完R_distance和检测到形状后，添加以下代码：
    
    # 假设您有以下变量：
    # R_distance - 实际距离
    # detected_shapes - 检测到的形状列表，每个形状包含height_mm和width_mm
    
    # 发送数据到STM32（仅在有有效数据时发送）
    if R_distance is not None and len(detected_shapes) > 0:
        # 准备形状数据
        shapes_to_send = []
        for shape_info in detected_shapes:
            shapes_to_send.append({
                'name': shape_info.get('name', 'Unknown'),  # 形状名称
                'height_mm': shape_info.get('real_height_mm', 0.0),  # 实际高度
                'width_mm': shape_info.get('real_width_mm', 0.0)     # 实际宽度
            })
        
        # 发送数据
        self.send_detection_data(R_distance, shapes_to_send)
    
    # ... 您的原有代码继续 ...
```

## 具体集成位置示例

根据您的代码结构，数据发送应该在以下位置调用：

1. **在计算完距离后**：当您有了有效的`R_distance`值
2. **在检测完形状后**：当您有了形状的`real_height_mm`和`real_width_mm`值
3. **在显示结果前**：确保数据已经准备好发送

典型的调用位置：
```python
# 您的原有代码中类似这样的位置：
if outer_rect_info is not None:
    # ... 计算距离和形状检测 ...
    
    # 如果检测到有效数据，发送给STM32
    if R_distance is not None and len(detected_shapes) > 0:
        self.send_detection_data(R_distance, detected_shapes)
    
    # ... 继续显示和其他处理 ...
```

## 注意事项

1. **数据格式**：确保传递给`send_detection_data`的形状数据包含正确的字段名
2. **错误处理**：函数内部已包含异常处理，不会影响主程序运行
3. **性能影响**：数据发送是异步的，不会显著影响检测性能
4. **调试输出**：函数会打印发送状态，便于调试

## 测试验证

修改完成后，您应该能看到类似的输出：
```
UART初始化完成: /dev/ttyS0, 波特率: 115200
发送数据包: 距离=245.6mm, 形状数量=2
发送数据包: 距离=251.3mm, 形状数量=1
```

这表示数据正在成功发送到STM32。
